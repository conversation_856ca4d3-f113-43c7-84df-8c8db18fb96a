import pandas as pd
from py3dbp import Packer, Bin, Item
import matplotlib.pyplot as plt
import numpy as np
import os
from collections import Counter

# --- Configuration ---
INPUT_EXCEL_FILE = 'items_to_pack.xlsx'
PLOT_IMAGE_FILE = 'packing_3d_plot.png'

# 40HQ Container Dimensions (Internal, approximate, in inches)
# These are common values, verify if your specific container has different internal dimensions
CONTAINER_LENGTH = 460.6  # Approx. 12.03 meters * 39.37 inches/meter
CONTAINER_WIDTH = 92.1   # Approx. 2.35 meters * 39.37 inches/meter
CONTAINER_HEIGHT = 104.6 # Approx. 2.69 meters * 39.37 inches/meter
CONTAINER_MAX_WEIGHT = 58000 # lbs (Approximate maximum payload capacity)

# Number of instances to create for each item type to maximize packing.
# We add many copies of each item type to the packer, and it will pack as many as fit.
NUM_ITEM_INSTANCES = 500 # Create 500 of each item type initially

# --- Helper Functions ---
def create_container_bin():
    """Creates the py3dbp Bin object for the 40HQ container."""
    # py3dbp Bin constructor: Bin(name, width, height, depth, max_weight)
    # Our constants: LENGTH=460.6, WIDTH=92.1, HEIGHT=104.6
    # To get correct dimensions: width=92.1, depth=460.6, height=104.6
    # We pass: CONTAINER_WIDTH, CONTAINER_HEIGHT, CONTAINER_LENGTH
    return Bin('40HQ Container', CONTAINER_WIDTH, CONTAINER_HEIGHT, CONTAINER_LENGTH, CONTAINER_MAX_WEIGHT)

def read_items_from_excel(file_path):
    """Reads item data from the specified Excel file."""
    try:
        df = pd.read_excel(file_path, sheet_name= 'items_to_pack')
        items_data = []
        required_cols = ['name', 'length', 'width', 'height', 'weight']

        # Check if all required columns exist (case-insensitive check)
        df.columns = df.columns.str.lower() # Convert column names to lowercase for robust checking
        if not all(col in df.columns for col in required_cols):
             missing_cols = [col for col in required_cols if col not in df.columns]
             raise ValueError(f"Excel file must contain the following columns: {required_cols}. Missing: {missing_cols}")

        # Iterate through each row to create item instances
        # We create multiple instances of each type to attempt to fill the container
        for index, row in df.iterrows():
            try:
                item_name = str(row['name']) # Ensure name is string
                length = float(row['length'])
                width = float(row['width'])
                height = float(row['height'])
                weight = float(row['weight'])

                # Basic validation
                if length <= 0 or width <= 0 or height <= 0 or weight <= 0:
                    print(f"Warning: Skipping item '{item_name}' (row {index+2}) due to non-positive dimensions or weight.")
                    continue

                # Create multiple instances of this item type
                for i in range(NUM_ITEM_INSTANCES):
                     # Give unique ID but keep original name identifier
                    # py3dbp requires unique names for items added to packer
                    items_data.append(Item(f"{item_name}_instance_{i+1}", width, length, height, weight)) # Note: py3dbp Item uses W, L, H order

            except (ValueError, TypeError) as e:
                print(f"Warning: Skipping row {index+2} due to invalid data type: {e}")
            except Exception as e:
                print(f"Warning: Skipping row {index+2} due to unexpected error: {e}")


        if not items_data:
            print("No valid items found in the Excel file after processing.")

        return items_data, df # Return the list of item instances and the original dataframe

    except FileNotFoundError:
        print(f"Error: Input file not found at {file_path}")
        return None, None
    except ValueError as ve:
        print(f"Error reading Excel file: {ve}")
        return None, None
    except Exception as e:
        print(f"An unexpected error occurred while reading Excel: {e}")
        return None, None

def pack_container(container_bin, items_to_pack):
    """Performs the packing using py3dbp."""
    if not items_to_pack:
        print("No items provided for packing.")
        return [], [] # Return empty lists for packed and unpacked

    packer = Packer()
    packer.add_bin(container_bin)

    # Add all item instances to the packer
    for item in items_to_pack:
        packer.add_item(item)

    print("Starting packing process... This may take a moment depending on the number of items.")
    packer.pack(bigger_first=True, distribute_items=False, number_of_decimals=2)
    print("Packing process finished.")

    # py3dbp places packed items in the bin's items list
    packed_items = packer.bins[0].items
    unpacked_items = packer.bins[0].unfitted_items

    return packed_items, unpacked_items

def analyze_packing_results(original_items_df, packed_items, container_bin):
    """Analyzes packed items and calculates metrics."""
    results = {}
    summary = {}

    if not packed_items:
        print("No items were packed.")
        summary['Total Items Packed (Total Count)'] = 0
        summary['Total Packed Volume (cu in)'] = 0
        summary['Total Packed Weight (lbs)'] = 0
    else:
        # Count packed items by their original name (remove the instance suffix)
        # We need to reconstruct the original name from the unique ID like "Box A_instance_123"
        packed_item_names = [item.name.rsplit('_instance_', 1)[0] for item in packed_items]
        packed_counts = Counter(packed_item_names)

        # Prepare item-level results
        item_results_list = []
        # Ensure we cover all item types originally requested, even if none were packed
        original_item_types = original_items_df['name'].tolist()

        for original_name in original_item_types:
            count = packed_counts.get(original_name, 0)

            # Find the original item dimensions/weight for this type from the input df
            # Filter original_items_df based on the original name
            original_item_info = original_items_df[original_items_df['name'].str.lower() == original_name.lower()]
            if not original_item_info.empty:
                # Take the first matching row (should be only one per name)
                original_item_info = original_item_info.iloc[0]
                # Use original dims/weight for total calculation per item type
                # Note: py3dbp's Item uses W, L, H, so map correctly
                item_length = original_item_info['length']
                item_width = original_item_info['width']
                item_height = original_item_info['height']
                item_weight = original_item_info['weight']
                item_volume = item_length * item_width * item_height # Calculate volume from original dims

                item_results_list.append({
                    'Item Name': original_name,
                    'Number Packed': count,
                    'Total Volume Packed (cu in)': count * item_volume,
                    'Total Weight Packed (lbs)': count * item_weight,
                    'Status': 'Packed' if count > 0 else 'Could not pack'
                })
            else:
                 # This shouldn't happen if logic is correct, but add safeguard
                 print(f"Warning: Original info not found for packed item type '{original_name}'. Skipping item details for this type.")


        results['item_details'] = pd.DataFrame(item_results_list)

        # Calculate summary statistics
        # calcualte total_packed volume from packed item details. 
        total_packed_volume = results['item_details']['Total Volume Packed (cu in)'].sum()
        total_packed_weight = results['item_details']['Total Weight Packed (lbs)'].sum()

        container_volume = container_bin.get_volume() # This uses the W, L, H order from Bin creation
        container_max_weight = container_bin.get_total_weight()

        volume_utilization_percent = (total_packed_volume / float(container_volume) * 100) if container_volume > 0 else 0
        weight_utilization_percent = (total_packed_weight / float(container_max_weight) * 100) if container_max_weight > 0 else 0

        summary['Total Items Packed (Total Count)'] = len(packed_items)
        summary['Total Packed Volume (cu in)'] = total_packed_volume
        summary['Total Container Volume (cu in)'] = container_volume
        summary['Volume Utilization (%)'] = volume_utilization_percent
        # Packing efficiency is often used interchangeably with Volume Utilization in this context
        summary['Packing Efficiency (%)'] = volume_utilization_percent # Using Volume Utilization as packing efficiency

        summary['Total Packed Weight (lbs)'] = total_packed_weight
        summary['Container Max Weight Capacity (lbs)'] = container_max_weight
        summary['Weight Utilization (%)'] = weight_utilization_percent

    results['summary'] = pd.DataFrame.from_dict(summary, orient='index', columns=['Value'])
    results['summary'].index.name = 'Metric'

    return results

def plot_packing(container_bin, packed_items, file_path):
    """Generates and saves a 3D plot of the packed container."""
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')

    # Plot container outline
    # py3dbp Bin dimensions are W, D, H (width, depth, height)
    # Map to matplotlib X, Y, Z: Width->X, Depth->Y, Height->Z
    container_size_xyz = (float(container_bin.width), float(container_bin.depth), float(container_bin.height)) # (W, D, H) for matplotlib X, Y, Z
    ax = plot_box(ax, (0, 0, 0), container_size_xyz, color='blue', alpha=0.1, label='Container Outline')

    # Plot packed items
    if packed_items:
        # Get unique original item names for coloring
        unique_item_names = sorted(list(set(item.name.rsplit('_instance_', 1)[0] for item in packed_items)))
        colors = plt.get_cmap('tab10', len(unique_item_names)) # Use a colormap
        item_color_map = {name: colors(i) for i, name in enumerate(unique_item_names)}

        # Plot all items
        for item in packed_items:
            original_name = item.name.rsplit('_instance_', 1)[0]
            pos = (float(item.position[0]), float(item.position[1]), float(item.position[2])) # (X, Y, Z)
            dims_rotated = item.get_dimension() # (W_rotated, D_rotated, H_rotated)
            item_size_xyz = (float(dims_rotated[0]), float(dims_rotated[1]), float(dims_rotated[2]))

            color = item_color_map[original_name]

            # Plot the box
            ax = plot_box(ax, pos, item_size_xyz, color=color, alpha=0.8)

            # Add text label (optional, can clutter for many items)
            # label_pos = (pos[0] + item_size_xyz[0]/2, pos[1] + item_size_xyz[1]/2, pos[2] + item_size_xyz[2]/2)
            # ax.text(*label_pos, original_name, color='black', ha='center', va='center', fontsize=6)


        # Set plot limits to container size
        ax.set_xlim(0, container_size_xyz[0])
        ax.set_ylim(0, container_size_xyz[1])
        ax.set_zlim(0, container_size_xyz[2])

        ax.set_xlabel('Width (inches)') # X-axis corresponds to Container Width
        ax.set_ylabel('Length (inches)') # Y-axis corresponds to Container Length
        ax.set_zlabel('Height (inches)') # Z-axis corresponds to Container Height
        ax.set_title('3D Packing Visualization')

        # Create a simple legend based on item types
        from matplotlib.lines import Line2D
        legend_handles = [Line2D([0], [0], marker='s', color='w', markerfacecolor=item_color_map[name],
                                     markersize=10, label=name)
                          for name in unique_item_names]
        ax.legend(handles=legend_handles, title="Item Types", loc='upper left', bbox_to_anchor=(1, 1))

    else:
        # Plot container outline even if no items are packed
        container_size_xyz = (float(container_bin.width), float(container_bin.depth), float(container_bin.height)) # (W, L, H) for matplotlib X, Y, Z
        ax = plot_box(ax, (0, 0, 0), container_size_xyz, color='blue', alpha=0.1, label='Container Outline')
        ax.set_xlim(0, container_size_xyz[0])
        ax.set_ylim(0, container_size_xyz[1])
        ax.set_zlim(0, container_size_xyz[2])
        ax.set_xlabel('Width (inches)')
        ax.set_ylabel('Length (inches)')
        ax.set_zlabel('Height (inches)')
        ax.set_title('3D Packing Visualization - No Items Packed')


    plt.tight_layout(rect=(0, 0, 0.85, 1)) # Adjust layout to make space for legend outside plot

    plt.savefig(file_path)
    print(f"3D plot saved to {file_path}")
    # plt.show() # Uncomment to display plot immediately

def plot_box(ax, position, dimensions, color, alpha, label=None):
    """Helper function to draw a 3D box."""
    # position is the corner (x, y, z)
    # dimensions are (dx, dy, dz) - size along x, y, z axes
    x, y, z = position
    dx, dy, dz = dimensions

    # Vertices of the box
    vertices = np.array([
        [x, y, z],
        [x+dx, y, z],
        [x+dx, y+dy, z],
        [x, y+dy, z],
        [x, y, z+dz],
        [x+dx, y, z+dz],
        [x+dx, y+dy, z+dz],
        [x, y+dy, z+dz]
    ])

    # Define the 6 faces by vertex indices
    # Each list of indices defines a face's corners in order
    faces = [
        [0, 1, 2, 3], # Bottom face
        [4, 5, 6, 7], # Top face
        [0, 1, 5, 4], # Side face 1 (front)
        [2, 3, 7, 6], # Side face 2 (back)
        [0, 3, 7, 4], # Side face 3 (left)
        [1, 2, 6, 5]  # Side face 4 (right)
    ]

    # Plot each face using Poly3DCollection for better alpha handling
    from mpl_toolkits.mplot3d.art3d import Poly3DCollection
    face_vertices = [vertices[face] for face in faces]
    ax.add_collection3d(Poly3DCollection(face_vertices, facecolors=color, linewidths=1, edgecolors='k', alpha=alpha))

    # Add label (only for the container outline for clarity)
    if label:
         # Place label near the corner (0,0,0) for the container
         ax.text(x, y, z, label, color='black', ha='left', va='bottom', fontsize=8)


    # Set plot limits (might be better to set once after all boxes are added)
    # ax.set_xlim([0, x + dx])
    # ax.set_ylim([0, y + dy])
    # ax.set_zlim([0, z + dz])

    return ax


def write_results_to_excel(results_dict, input_file_path):
    """Writes the packing results to the input Excel file in new sheets."""
    try:
        # Read the existing Excel file to preserve existing sheets
        with pd.ExcelFile(input_file_path) as xls:
            existing_sheets = {}
            for sheet_name in xls.sheet_names:
                existing_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)

        # Write back all existing sheets plus new result sheets
        with pd.ExcelWriter(input_file_path, engine='openpyxl') as writer:
            # Write existing sheets first
            for sheet_name, df in existing_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

            # Write Item Packing Summary
            if 'item_details' in results_dict and not results_dict['item_details'].empty:
                results_dict['item_details'].to_excel(writer, sheet_name='Packing_Results', index=False)
            else:
                 empty_df = pd.DataFrame([['No items packed or item data available.']], columns=['Message'])
                 empty_df.to_excel(writer, sheet_name='Packing_Results', index=False)

            # Write Summary Statistics
            if 'summary' in results_dict and not results_dict['summary'].empty:
                 results_dict['summary'].to_excel(writer, sheet_name='Summary_Statistics')
            else:
                 empty_df = pd.DataFrame([['No summary statistics available.']], columns=['Message'])
                 empty_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

        print(f"Packing results saved to input file: {input_file_path}")
        print("New sheets added: 'Packing_Results' and 'Summary_Statistics'")

    except Exception as e:
        print(f"Error writing results to Excel: {e}")
        print("Attempting to create backup output file...")
        # Fallback: create separate output file if input file modification fails
        backup_file = input_file_path.replace('.xlsx', '_packing_results.xlsx')
        try:
            with pd.ExcelWriter(backup_file) as writer:
                if 'item_details' in results_dict and not results_dict['item_details'].empty:
                    results_dict['item_details'].to_excel(writer, sheet_name='Packing_Results', index=False)
                if 'summary' in results_dict and not results_dict['summary'].empty:
                    results_dict['summary'].to_excel(writer, sheet_name='Summary_Statistics')
            print(f"Backup results saved to: {backup_file}")
        except Exception as backup_error:
            print(f"Failed to create backup file: {backup_error}")


# --- Main Execution ---
if __name__ == "__main__":
    # Get the directory of the script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_path = os.path.join(script_dir, INPUT_EXCEL_FILE)
    plot_path = os.path.join(script_dir, PLOT_IMAGE_FILE)

    print(script_dir)

    print("--- Container Packing Solution ---")
    print(f"Using py3dbp library.")
    print(f"Container: 40HQ (Internal: {CONTAINER_LENGTH}L x {CONTAINER_WIDTH}W x {CONTAINER_HEIGHT}H inches, Max Weight: {CONTAINER_MAX_WEIGHT} lbs)")
    print(f"Reading items from: {INPUT_EXCEL_FILE}")
    print(f"Results will be saved to new sheets in the input file: {INPUT_EXCEL_FILE}")

    # Read items from Excel
    all_items_for_packing, original_items_df = read_items_from_excel(input_path)

    if all_items_for_packing is None or not all_items_for_packing:
        print("Exiting due to errors reading items or no valid items found.")
        # Still create empty output files to indicate process ran
        write_results_to_excel({}, input_path) # Write empty results to input file
        # Create a placeholder plot or just the container outline
        container = create_container_bin()
        plot_packing(container, [], plot_path) # Plot just the container outline

    else:
        print(f"Read {len(original_items_df) if original_items_df is not None else 0} unique item types from Excel.")
        print(f"Created {len(all_items_for_packing)} item instances for packing attempt.")

        # Create the container bin
        container = create_container_bin()
        # py3dbp Bin dimensions are (W, L, H) - let's print them in standard (L, W, H) for clarity
        print(f"Created py3dbp Bin: {container.name} ({container.depth}L x {container.width}W x {container.height}H inches, Max Weight: {container.max_weight} lbs)")


        # Perform packing
        packed_items, unpacked_items = pack_container(container, all_items_for_packing)
        print(f"Packed {len(packed_items)} items and left {len(unpacked_items)} items unpacked.")

        # Analyze results
        print("Analyzing packing results...")
        results_data = analyze_packing_results(original_items_df, packed_items, container)
        print("Analysis complete.")

        # Print summary to console (optional)
        if 'summary' in results_data:
             print("\n--- Summary Statistics ---")
             print(results_data['summary'].to_string()) # Use to_string for better console output

        # Generate plot
        print("Generating 3D plot...")
        plot_packing(container, packed_items, plot_path)

        # Write results to input Excel file
        write_results_to_excel(results_data, input_path)

    print("\n--- Process Finished ---")